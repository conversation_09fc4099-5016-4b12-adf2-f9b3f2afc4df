import { getCurrentUserApi } from '@/backend/api';
import { UnauthorizedError } from '@/backend/errors';
import { NextResponse } from 'next/server';

export async function GET() {
	try {
		const user = await getCurrentUserApi();

		return NextResponse.json(user);
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json(
				{ error: error.message },
				{ status: 401 }
			);
		}

		console.error('Get current user error:', error);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
