'use server';

import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getCollectionService } from '@/backend/wire'; // Assumed: Service getter from wire
import { auth } from '@/lib';
import { CollectionWithDetail } from '@/models'; // Assuming this type is updated externally
import { Language } from '@prisma/client'; // Import Language enum

/**
 * Deletes a collection for the authenticated user.
 * @param collectionId - The ID of the collection to delete.
 * @returns A boolean indicating if the deletion was successful.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId is missing.
 * @throws {NotFoundError} If collection is not found by the service.
 * @throws {Error} If deletion fails for other reasons.
 */
export async function deleteCollectionApi(collectionId: string): Promise<boolean> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}

	const collectionService = getCollectionService();
	try {
		// Assuming collectionService.deleteCollection might throw NotFoundError if applicable
		const result = await collectionService.deleteCollection(userId, collectionId);
		return result;
	} catch (error) {
		if (
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError ||
			error instanceof ValidationError
		) {
			throw error;
		}
		// Log for server, generic error for client
		console.error(`Failed to delete collection ${collectionId} for user ${userId}:`, error);
		throw new Error('Failed to delete collection');
	}
}

/**
 * Updates a collection's name, target language, source language, or word list for the authenticated user.
 * @param collectionId - The ID of the collection to update.
 * @param data - Object containing optional `name` (string), `target_language` (Language), `source_language` (Language), and `wordIds` (string[]).
 * @returns The updated collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId is missing, or if name/language values are invalid.
 * @throws {NotFoundError} If collection is not found.
 * @throws {Error} If update fails for other reasons.
 */
export async function updateCollectionApi(
	collectionId: string,
	data: {
		name?: string;
		wordIds?: string[];
	}
): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!data || typeof data !== 'object') {
		throw new ValidationError('Update data object is required.');
	}

	const { name, wordIds } = data;

	const collectionService = getCollectionService();
	try {
		// Pass the new fields to the service update method
		const updatedCollection = await collectionService.updateCollection(
			userId,
			collectionId,
			name,
			wordIds
		);
		if (!updatedCollection) {
			// This case handles if service returns null/undefined for not found when not throwing NotFoundError itself
			throw new NotFoundError('Collection', collectionId);
		}
		return updatedCollection as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError
		) {
			throw error;
		}
		console.error(`Failed to update collection ${collectionId} for user ${userId}:`, error);
		throw new Error('Failed to update collection');
	}
}

/**
 * Retrieves a specific collection for the authenticated user.
 * @param collectionId - The ID of the collection to retrieve.
 * @returns The requested collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId is missing.
 * @throws {NotFoundError} If collection is not found.
 * @throws {Error} If fetch fails for other reasons.
 */
export async function getCollectionApi(collectionId: string): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}

	const collectionService = getCollectionService();
	try {
		const collection = await collectionService.getCollectionById(userId, collectionId);
		if (!collection) {
			throw new NotFoundError('Collection', collectionId);
		}
		return collection as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError ||
			error instanceof ValidationError
		) {
			throw error;
		}
		console.error(`Failed to fetch collection ${collectionId} for user ${userId}:`, error);
		throw new Error('Failed to fetch collection');
	}
}

/**
 * Retrieves all collections for the authenticated user.
 * @returns An array of collections, including `target_language` and `source_language` for each.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {Error} If fetch fails for other reasons.
 */
export async function getCollectionsApi(): Promise<CollectionWithDetail[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError('User not authenticated');
	}

	const collectionService = getCollectionService();
	try {
		const collections = await collectionService.getUserCollections(userId);
		return collections as CollectionWithDetail[]; // Service might return CollectionWithDetailDeep[]
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Failed to fetch collections for user ${userId}:`, error);
		throw new Error('Failed to fetch collections');
	}
}

/**
 * Creates a new collection for the authenticated user.
 * @param name - The name of the new collection.
 * @param target_language - The target language of the collection.
 * @param source_language - The source language of the collection.
 * @param wordIds - Optional array of word IDs to add to the new collection.
 * @returns The newly created collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If name, target_language, or source_language is missing or invalid.
 * @throws {Error} If creation fails for other reasons.
 */
export async function createCollectionApi(
	name: string,
    target_language: Language, // Add new field parameter
    source_language: Language, // Add new field parameter
	wordIds?: string[]
): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!name) {
		throw new ValidationError('Collection name is required');
	}
	// Add validation for new fields
	if (!target_language || !Object.values(Language).includes(target_language)) {
		throw new ValidationError(`Invalid target_language provided: ${target_language}`);
	}
	if (!source_language || !Object.values(Language).includes(source_language)) {
		throw new ValidationError(`Invalid source_language provided: ${source_language}`);
	}


	const collectionService = getCollectionService();
	try {
		// Pass the new fields to the service create method
		const collection = await collectionService.createCollection(userId, name, target_language, source_language, wordIds);
		if (!collection) {
			// If service doesn't throw on creation failure but returns null/undefined
			throw new Error('Failed to create collection');
		}
		return collection as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Failed to create collection for user ${userId}:`, error);
		throw new Error('Failed to create collection');
	}
}

/**
 * Adds a word to a collection for the authenticated user.
 * @param collectionId - The ID of the collection to add the word to.
 * @param wordId - The ID of the word to add.
 * @returns The updated collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId or wordId is missing.
 * @throws {NotFoundError} If collection is not found.
 * @throws {Error} If adding word fails for other reasons.
 */
export async function addWordToCollectionApi(
	collectionId: string,
	wordId: string
): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!wordId) {
		throw new ValidationError('Word ID is required');
	}
	if (wordId.trim() === '') {
		throw new ValidationError('Word ID cannot be empty or whitespace.');
	}

	const collectionService = getCollectionService();
	try {
		const result = await collectionService.addWordsToCollection(userId, collectionId, [wordId]);
		if (!result) {
			// Assuming service returns null/undefined if collection not found or operation failed
			throw new NotFoundError('Collection', collectionId); // Or a more generic error like 'Failed to add word'
		}
		return result as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError
		) {
			throw error;
		}
		console.error(
			`Failed to add word ${wordId} to collection ${collectionId} for user ${userId}:`,
			error
		);
		throw new Error('Failed to add word to collection');
	}
}

/**
 * Adds a single term to a collection for the authenticated user.
 * This involves creating a word from the term if it doesn't exist and then adding it to the collection.
 * @param collectionId - The ID of the collection to add the term to.
 * @param term - The term (string) to add.
 * @param language - The language of the term.
 * @returns The updated collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId, term, or language is missing.
 * @throws {NotFoundError} If collection is not found.
 * @throws {Error} If adding term fails for other reasons.
 */
export async function addTermToCollectionApi(
	collectionId: string,
	term: string,
	language: Language
): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!term) {
		throw new ValidationError('Term is required');
	}
	if (!language) {
		throw new ValidationError('Language is required');
	}
	// Check if the provided language is a valid enum value
	if (!Object.values(Language).includes(language)) {
		throw new ValidationError(`Invalid language provided: ${language}`);
	}

	const collectionService = getCollectionService();
	try {
		const result = await collectionService.addTermsToCollection(
			userId,
			collectionId,
			[term],
			language
		);
		if (!result) {
			throw new NotFoundError('Collection', collectionId); // Or a more generic error
		}
		return result as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError
		) {
			throw error;
		}
		console.error(
			`Failed to add term "${term}" to collection ${collectionId} for user ${userId}:`,
			error
		);
		throw new Error('Failed to add term to collection');
	}
}

/**
 * Removes specified words from a collection for the authenticated user.
 * @param collectionId - The ID of the collection to remove words from.
 * @param wordIds - An array of word IDs to remove.
 * @returns The updated collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId or wordIds are missing/empty.
 * @throws {NotFoundError} If collection is not found.
 * @throws {Error} If removing words fails for other reasons.
 */
export async function removeWordsFromCollectionApi(
	collectionId: string,
	wordIds: string[]
): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!wordIds || wordIds.length === 0) {
		throw new ValidationError('Word IDs are required to remove words from collection');
	}
	if (wordIds.some((id) => typeof id !== 'string' || id.trim() === '')) {
		throw new ValidationError('Each word ID in wordIds must be a non-empty string.');
	}

	const collectionService = getCollectionService();
	try {
		const result = await collectionService.removeWordsFromCollection(
			userId,
			collectionId,
			wordIds
		);
		if (!result) {
			throw new NotFoundError('Collection', collectionId); // Or a more generic error
		}
		return result as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError
		) {
			throw error;
		}
		console.error(
			`Failed to remove words from collection ${collectionId} for user ${userId}:`,
			error
		);
		throw new Error('Failed to remove words from collection');
	}
}

/**
 * Adds multiple words to a collection for the authenticated user.
 * @param collectionId - The ID of the collection to add words to.
 * @param wordIds - An array of word IDs to add.
 * @returns The updated collection, including `target_language` and `source_language`.
 * @throws {UnauthorizedError} If user is not authenticated.
 * @throws {ValidationError} If collectionId or wordIds are missing/empty.
 * @throws {NotFoundError} If collection is not found.
 * @throws {Error} If adding words fails for other reasons.
 */
export async function addWordsToCollectionApi(
	collectionId: string,
	wordIds: string[]
): Promise<CollectionWithDetail> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!wordIds || wordIds.length === 0) {
		throw new ValidationError('Word IDs are required to add to collection');
	}
	if (wordIds.some((id) => typeof id !== 'string' || id.trim() === '')) {
		throw new ValidationError('Each word ID in wordIds must be a non-empty string.');
	}

	const collectionService = getCollectionService();
	try {
		const result = await collectionService.addWordsToCollection(userId, collectionId, wordIds);
		if (!result) {
			throw new NotFoundError('Collection', collectionId); // Or a more generic error
		}
		return result as CollectionWithDetail; // Service might return CollectionWithDetailDeep
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof NotFoundError ||
			error instanceof UnauthorizedError
		) {
			throw error;
		}
		console.error(
			`Failed to add words to collection ${collectionId} for user ${userId}:`,
			error
		);
		throw new Error('Failed to add words to collection');
	}
}
