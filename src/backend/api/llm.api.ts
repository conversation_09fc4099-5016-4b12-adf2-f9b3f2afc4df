'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import {
	AnswerEvaluationResult,
	EvaluateAnswersParams,
	EvaluateTranslationParams,
	GenerateQuestionsParams,
	GenerateParagraphWithQuestionsParams,
	ParagraphWithQuestionsResult,
	TranslationEvaluationResult,
	GrammarPracticeParams,
	GrammarPracticeResultItem,
} from '@/backend/services';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { RandomWord } from '@/models';
import { Difficulty, Language, Word } from '@prisma/client';
import { z, ZodError } from 'zod';
import fs from 'fs/promises';
import path from 'path';

// Base schemas for reusable validation
const languageSchema = z.nativeEnum(Language);
const difficultySchema = z.nativeEnum(Difficulty);
const nonEmptyStringSchema = z.string().min(1, 'Must be a non-empty string');
const nonEmptyStringArraySchema = z.array(nonEmptyStringSchema).min(1, 'Array cannot be empty');

// Generate Random Words validation
const generateRandomWordsSchema = z.object({
	keywordTerms: nonEmptyStringArraySchema,
	maxTerms: z.number().int().min(1).max(50, 'Max terms must be between 1 and 50'),
	excludeCollectionIds: z.array(nonEmptyStringSchema).optional().default([]),
	source_language: languageSchema,
	target_language: languageSchema,
});

// Generate Word Details validation
const generateWordDetailsSchema = z.object({
	terms: nonEmptyStringArraySchema.max(10, 'Maximum of 10 terms allowed'),
	source_language: languageSchema,
	target_language: languageSchema,
});

// Generate Paragraph validation
const generateParagraphSchema = z.object({
	keywords: nonEmptyStringArraySchema,
	language: languageSchema,
	difficulty: difficultySchema,
	count: z.number().int().min(1).max(5, 'Count must be between 1 and 5'),
	sentenceCount: z.number().int().min(1).optional(),
});

// Grammar Practice validation
const grammarPracticeSchema = z.object({
	keywords: nonEmptyStringArraySchema,
	language: languageSchema,
	source_language: languageSchema,
	target_language: languageSchema,
	difficulty: difficultySchema,
	count: z.number().int().min(1).max(5, 'Count must be between 1 and 5'),
	sentenceCount: z.number().int().min(1).optional(),
	errorDensity: z.enum(['low', 'medium', 'high']).optional(),
})
.refine((data) => data.source_language !== data.target_language, {
	message: 'Source and target languages must be different',
	path: ['target_language'],
});

// Evaluate Translation validation
const evaluateTranslationSchema = z
	.object({
		original_text: nonEmptyStringSchema,
		translated_text: nonEmptyStringSchema,
		source_language: languageSchema,
		target_language: languageSchema,
	})
	.refine((data) => data.source_language !== data.target_language, {
		message: 'Source and target languages must be different',
		path: ['target_language'],
	});

// Generate Questions validation
const generateQuestionsSchema = z.object({
	paragraph: nonEmptyStringSchema,
	language: languageSchema,
	questionCount: z.number().int().min(1).max(10, 'Question count must be between 1 and 10'),
});

// Generate Paragraph with Questions validation
const generateParagraphWithQuestionsSchema = z.object({
	keywords: nonEmptyStringArraySchema,
	language: languageSchema,
	difficulty: difficultySchema,
	sentenceCount: z.number().int().min(1).optional(),
	questionCount: z.number().int().min(1).max(10, 'Question count must be between 1 and 10'),
});

// Evaluate Answers validation
const evaluateAnswersSchema = z
	.object({
		paragraph: nonEmptyStringSchema,
		questions: nonEmptyStringArraySchema,
		answers: z.array(z.string()), // Allow empty strings for skipped answers
		qna_language: languageSchema,
		feedback_native_language: languageSchema,
	})
	.refine((data) => data.questions.length === data.answers.length, {
		message: 'The number of questions and answers must match',
		path: ['answers'],
	});

// Type exports for the validated data
export type GenerateRandomWordsInput = z.infer<typeof generateRandomWordsSchema>;
export type GenerateWordDetailsInput = z.infer<typeof generateWordDetailsSchema>;
export type GenerateParagraphInput = z.infer<typeof generateParagraphSchema>;
export type GrammarPracticeInput = z.infer<typeof grammarPracticeSchema>;
export type EvaluateTranslationInput = z.infer<typeof evaluateTranslationSchema>;
export type GenerateQuestionsInput = z.infer<typeof generateQuestionsSchema>;
export type GenerateParagraphWithQuestionsInput = z.infer<
	typeof generateParagraphWithQuestionsSchema
>;
export type EvaluateAnswersInput = z.infer<typeof evaluateAnswersSchema>;

/**
 * Converts Zod validation errors to ValidationError
 */
function handleValidationError(error: ZodError): never {
	const message = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`).join('; ');
	throw new ValidationError(message);
}

/**
 * Generates random terms (words) for the authenticated user based on keywords,
 * with options to exclude terms from certain collections.
 * Uses local JSON file caching in development mode for better performance.
 * @param keywordTerms - Array of keyword strings to base generation on.
 * @param maxTerms - Maximum number of terms to generate (1-50).
 * @param excludeCollectionIds - Optional array of collection IDs whose terms should be excluded.
 * @param source_language - The user's native language.
 * @param target_language - The language being learned.
 * @returns A promise that resolves to an array of RandomWord objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If keywordTerms are missing/empty or maxTerms is out of range, or languages are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateRandomWordsApi(
	keywordTerms: string[],
	maxTerms: number,
	excludeCollectionIds: string[] = [],
	source_language: Language, // Add source_language
	target_language: Language // Add target_language
): Promise<RandomWord[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId)
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate random words.'
		);

	// Validate input using Zod
	try {
		generateRandomWordsSchema.parse({
			keywordTerms,
			maxTerms,
			excludeCollectionIds,
			source_language,
			target_language,
		});
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	// Generate cache filename based on parameters
	const sortedKeywords = [...keywordTerms].sort();
	const keywordsStr = sortedKeywords
		.map((keyword) => keyword.replace(/[^a-zA-Z0-9_]+/g, '_'))
		.join('_');
	const sortedExcludeIds = [...excludeCollectionIds].sort();
	const excludeIdsStr =
		sortedExcludeIds.length > 0 ? `_exclude${sortedExcludeIds.join('_')}` : '';
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`randomWords_${keywordsStr}_max${maxTerms}${excludeIdsStr}_${source_language}_${target_language}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as RandomWord[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
			// Fall through to generate if file not found or other read error
		}
	}

	const llmService = getLLMService();
	try {
		// The original GenerateRandomTermsQuery had an `excludesTerms` parameter.
		// This API signature doesn't. Assuming an empty array for it.
		// If `excludesTerms` needs to be passed from the client, update the API signature.
		const result = await llmService.generateRandomTerms({
			userId: userId,
			keywordTerms: keywordTerms,
			excludesTerms: [], // Defaulting to empty array as it's not in API params
			maxTerms: maxTerms,
			excludeCollectionIds: excludeCollectionIds,
			source_language: source_language, // Pass source_language
			target_language: target_language, // Pass target_language
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save random words cache file ${filePath}:`,
					err.message
				);
			}
		}

		return result;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateRandomWordsApi for user ${userId}:`, error);
		throw new Error('Failed to generate random words. Please try again.');
	}
}

/**
 * Generates detailed information for a list of terms.
 * Uses a local JSON file for caching in development, with filenames based on terms.
 * This API does not require authentication as per its original design.
 * If user-specific details or auth is needed, `auth()` call should be added.
 * @param terms - Array of term strings to get details for (max 10).
 * @param source_language - The user's native language.
 * @param target_language - The language being learned.
 * @returns A promise that resolves to an array of Word objects.
 * @throws {ValidationError} If terms array is missing, empty, exceeds max length, or languages are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateWordDetailsApi(
	terms: string[],
	source_language: Language, // Add source_language
	target_language: Language // Add target_language
): Promise<Word[]> {
	// Validate input using Zod
	try {
		generateWordDetailsSchema.parse({
			terms,
			source_language,
			target_language,
		});
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	// Generate a filename based on the terms for caching
	const sortedTerms = [...terms].sort();
	const filenameBase = sortedTerms.map((term) => term.replace(/[^a-zA-Z0-9_]+/g, '_')).join('_');
	// Include languages in cache key to avoid collision if same terms are requested for different languages
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`wordDetails_${filenameBase}_${source_language}_${target_language}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as Word[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
			// Fall through to generate if file not found or other read error
		}
	}

	const llmService = getLLMService();
	try {
		// Pass source_language and target_language to the service method
		const result = await llmService.generateWordDetails(
			terms,
			source_language,
			target_language
		);

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save word details cache file ${filePath}:`,
					err.message
				);
			}
		}
		return result;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error('Error in generateWordDetailsApi:', error);
		throw new Error('Failed to generate word details. Please try again.');
	}
}

/**
 * Generates a specified number of paragraphs based on keywords, language, difficulty, and length.
 * This API requires authentication.
 * @param keywords - Array of keyword strings to base generation on.
 * @param language - The language of the paragraphs to be generated (source language for user).
 * @param difficulty - The difficulty level of the paragraphs.
 * @param length - The desired length of the paragraphs.
 * @param count - The number of paragraphs to generate (1-5).
 * @returns A promise that resolves to an array of generated paragraph strings.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If paragraph generation fails for other reasons.
 */
export async function generateParagraphApi(
	keywords: string[],
	language: Language,
	difficulty: Difficulty,
	count: number,
	sentenceCount?: number
): Promise<string[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate paragraphs.'
		);
	}

	// Validate input using Zod
	try {
		generateParagraphSchema.parse({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
		});
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	// Generate cache filename based on parameters
	const sortedKeywords = [...keywords].sort();
	const keywordsStr = sortedKeywords
		.map((keyword) => keyword.replace(/[^a-zA-Z0-9_]+/g, '_'))
		.join('_');
	const sentenceCountStr = sentenceCount ? `_sentences${sentenceCount}` : '';
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`paragraph_${keywordsStr}_${language}_${difficulty}_count${count}${sentenceCountStr}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as string[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		const paragraphs = await llmService.generateParagraph({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
			// userId is not directly used by llmService.generateParagraph in the provided service code,
			// but it's good to have it if the service evolves to use it for personalization or logging.
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(paragraphs, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save paragraph cache file ${filePath}:`,
					err.message
				);
			}
		}
		return paragraphs;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateParagraphApi for user ${userId}:`, error);
		throw new Error('Failed to generate paragraphs. Please try again.');
	}
}

/**
 * Generates paragraphs with intentional grammar errors based on provided keywords and difficulty.
 * @param params - Parameters for generating paragraphs with errors.
 * @returns An array of generated paragraphs with errors.
 * @throws {ValidationError} If input parameters are invalid.
 * @throws {UnauthorizedError} If the user is not authenticated.
 */
export async function generateGrammarPracticeApi(
	params: GrammarPracticeParams
): Promise<GrammarPracticeResultItem[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('User not authenticated for generating paragraphs.');

	// Validate input using Zod
	try {
		grammarPracticeSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	// Generate cache filename based on parameters
	const { keywords, language, source_language, target_language, difficulty, count, sentenceCount, errorDensity } = params;
	const sortedKeywords = [...keywords].sort();
	const keywordsStr = sortedKeywords
		.map((keyword) => keyword.replace(/[^a-zA-Z0-9_]+/g, '_'))
		.join('_');
	const sentenceCountStr = sentenceCount ? `_sentences${sentenceCount}` : '';
	const errorDensityStr = errorDensity ? `_${errorDensity}` : '';
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`grammarPractice_${keywordsStr}_${language}_${source_language}_${target_language}_${difficulty}_count${count}${sentenceCountStr}${errorDensityStr}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as GrammarPracticeResultItem[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		const paragraphs = await llmService.generateGrammarPractice(params);

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(paragraphs, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save grammar practice cache file ${filePath}:`,
					err.message
				);
			}
		}
		return paragraphs;
	} catch (error) {
		console.error('Error in generateParagraphsWithErrorsApi:', error);
		if (error instanceof Error) {
			throw error;
		}
		throw new Error('Failed to generate paragraphs with errors due to an internal issue.');
	}
}

/**
 * Evaluates a user's translation of a given text.
 * This API requires authentication.
 * @param params - Object containing original_text, translated_text, source_language, and target_language.
 * @returns A promise that resolves to a TranslationEvaluationResult object.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If translation evaluation fails for other reasons.
 */
export async function evaluateTranslationApi(
	params: EvaluateTranslationParams
): Promise<TranslationEvaluationResult> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to evaluate translations.'
		);
	}

	// Validate input using Zod
	try {
		evaluateTranslationSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	const { original_text, translated_text, source_language, target_language } = params;

	// Generate cache filename based on parameters
	const originalTextHash = Buffer.from(original_text)
		.toString('base64')
		.replace(/[^a-zA-Z0-9]/g, '')
		.substring(0, 20);
	const translatedTextHash = Buffer.from(translated_text)
		.toString('base64')
		.replace(/[^a-zA-Z0-9]/g, '')
		.substring(0, 20);
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`evaluateTranslation_${originalTextHash}_${translatedTextHash}_${source_language}_${target_language}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as TranslationEvaluationResult;
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		const evaluation = await llmService.evaluateTranslation({
			original_text,
			translated_text,
			source_language,
			target_language,
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(evaluation, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save translation evaluation cache file ${filePath}:`,
					err.message
				);
			}
		}
		return evaluation;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in evaluateTranslationApi for user ${userId}:`, error);
		throw new Error('Failed to evaluate translation. Please try again.');
	}
}

/**
 * Generates a specified number of questions based on a paragraph.
 * This API requires authentication.
 * @param params - Object containing paragraph, language, and questionCount.
 * @returns A promise that resolves to an array of generated question strings.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If question generation fails for other reasons.
 */
export async function generateQuestionsApi(params: GenerateQuestionsParams): Promise<string[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate questions.'
		);
	}

	// Validate input using Zod
	try {
		generateQuestionsSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	const { paragraph, language, questionCount } = params;

	// Generate cache filename based on parameters
	const paragraphHash = Buffer.from(paragraph)
		.toString('base64')
		.replace(/[^a-zA-Z0-9]/g, '')
		.substring(0, 20);
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`generateQuestions_${paragraphHash}_${language}_count${questionCount}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as string[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		const questions = await llmService.generateQuestions({
			paragraph,
			language,
			questionCount,
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(questions, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save questions cache file ${filePath}:`,
					err.message
				);
			}
		}
		return questions;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateQuestionsApi for user ${userId}:`, error);
		throw new Error('Failed to generate questions. Please try again.');
	}
}

/**
 * Generates a paragraph with questions in a single API call.
 * This API requires authentication and combines paragraph and question generation for efficiency.
 * @param params - Object containing keywords, language, difficulty, sentenceCount, and questionCount.
 * @returns A promise that resolves to an object with paragraph and questions.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateParagraphWithQuestionsApi(
	params: GenerateParagraphWithQuestionsParams
): Promise<ParagraphWithQuestionsResult> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate paragraph with questions.'
		);
	}

	// Validate input using Zod
	try {
		generateParagraphWithQuestionsSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	const { keywords, language, difficulty, sentenceCount, questionCount } = params;

	// Generate cache filename based on parameters
	const sortedKeywords = [...keywords].sort();
	const keywordsStr = sortedKeywords
		.map((keyword) => keyword.replace(/[^a-zA-Z0-9_]+/g, '_'))
		.join('_');
	const sentenceCountStr = sentenceCount ? `_sentences${sentenceCount}` : '';
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`paragraphWithQuestions_${keywordsStr}_${language}_${difficulty}_questions${questionCount}${sentenceCountStr}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as ParagraphWithQuestionsResult;
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		const result = await llmService.generateParagraphWithQuestions({
			keywords,
			language,
			difficulty,
			sentenceCount,
			questionCount,
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save paragraph with questions cache file ${filePath}:`,
					err.message
				);
			}
		}
		return result;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateParagraphWithQuestionsApi for user ${userId}:`, error);
		throw new Error('Failed to generate paragraph with questions. Please try again.');
	}
}

/**
 * Evaluates user's answers to questions based on a paragraph.
 * This API requires authentication.
 * @param params - Object containing paragraph, questions, answers, qna_language, and feedback_native_language.
 * @returns A promise that resolves to an array of AnswerEvaluationResult objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If answer evaluation fails for other reasons.
 */
export async function evaluateAnswersApi(
	params: EvaluateAnswersParams
): Promise<AnswerEvaluationResult[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to evaluate answers.'
		);
	}

	// Validate input using Zod
	try {
		evaluateAnswersSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) {
			handleValidationError(error);
		}
		throw error;
	}

	const { paragraph, questions, answers, qna_language, feedback_native_language } = params;

	// Generate cache filename based on parameters
	const paragraphHash = Buffer.from(paragraph)
		.toString('base64')
		.replace(/[^a-zA-Z0-9]/g, '')
		.substring(0, 20);
	const questionsHash = Buffer.from(questions.join('|'))
		.toString('base64')
		.replace(/[^a-zA-Z0-9]/g, '')
		.substring(0, 20);
	const answersHash = Buffer.from(answers.join('|'))
		.toString('base64')
		.replace(/[^a-zA-Z0-9]/g, '')
		.substring(0, 20);
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`evaluateAnswers_${paragraphHash}_${questionsHash}_${answersHash}_${qna_language}_${feedback_native_language}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as AnswerEvaluationResult[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		const evaluations = await llmService.evaluateAnswers({
			paragraph,
			questions,
			answers,
			qna_language,
			feedback_native_language,
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				// Ensure cache directory exists
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(evaluations, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save answer evaluation cache file ${filePath}:`,
					err.message
				);
			}
		}
		return evaluations;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in evaluateAnswersApi for user ${userId}:`, error);
		throw new Error('Failed to evaluate answers. Please try again.');
	}
}
